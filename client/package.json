{"name": "gemini-cli-gui-client", "version": "1.0.0", "description": "Frontend client for Gemini CLI GUI", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8"}}