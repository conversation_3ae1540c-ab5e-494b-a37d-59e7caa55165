import React, { useState } from 'react';
import { X, Play } from 'lucide-react';
import { useProjects } from '../contexts/ProjectContext';

const NewSessionModal = ({ isOpen, project, onClose }) => {
  const { createSession } = useProjects();
  const [formData, setFormData] = useState({
    name: '',
    prompt: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setError('Session name is required');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Sanitize session name (replace spaces with dashes)
      const sanitizedName = formData.name.trim().replace(/\s+/g, '-');
      
      await createSession(project.id, sanitizedName, formData.prompt.trim());
      onClose();
      setFormData({ name: '', prompt: '' });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleNameChange = (e) => {
    const value = e.target.value;
    // Show the sanitized version in real-time
    setFormData(prev => ({ ...prev, name: value }));
  };

  const getSanitizedName = () => {
    return formData.name.trim().replace(/\s+/g, '-');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">New Session</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Project Info */}
        <div className="mb-4 p-3 bg-gray-700 rounded-lg">
          <div className="text-sm text-gray-300">Project:</div>
          <div className="font-medium">{project.name}</div>
          <div className="text-xs text-gray-400">{project.path}</div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Session Name */}
          <div>
            <label htmlFor="sessionName" className="block text-sm font-medium mb-2">
              Session/Worktree Name
            </label>
            <input
              id="sessionName"
              type="text"
              value={formData.name}
              onChange={handleNameChange}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter session name"
              disabled={loading}
            />
            {formData.name && formData.name !== getSanitizedName() && (
              <p className="text-xs text-gray-400 mt-1">
                Will be saved as: <span className="text-blue-400">{getSanitizedName()}</span>
              </p>
            )}
          </div>

          {/* Initial Prompt */}
          <div>
            <label htmlFor="initialPrompt" className="block text-sm font-medium mb-2">
              Initial Prompt (Optional)
            </label>
            <textarea
              id="initialPrompt"
              value={formData.prompt}
              onChange={(e) => setFormData(prev => ({ ...prev, prompt: e.target.value }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
              placeholder="Enter your initial prompt for Gemini CLI..."
              rows={3}
              disabled={loading}
            />
            <p className="text-xs text-gray-400 mt-1">
              This will be passed to Gemini CLI with the --prompt flag
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-900 border border-red-700 rounded-lg text-red-200 text-sm">
              {error}
            </div>
          )}

          {/* Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 rounded-lg transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
              disabled={loading || !formData.name.trim()}
            >
              <Play size={16} />
              {loading ? 'Starting...' : 'Start Session'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewSessionModal;
