import React, { useState, useEffect } from 'react';
import { io } from 'socket.io-client';
import Sidebar from './components/Sidebar';
import TerminalView from './components/TerminalView';
import SettingsModal from './components/SettingsModal';
import { ProjectProvider } from './contexts/ProjectContext';
import { TerminalProvider } from './contexts/TerminalContext';

function App() {
  const [socket, setSocket] = useState(null);
  const [showSettings, setShowSettings] = useState(false);
  const [currentProject, setCurrentProject] = useState(null);
  const [currentSession, setCurrentSession] = useState(null);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io('http://localhost:3001');
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server');
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
    });

    return () => {
      newSocket.close();
    };
  }, []);

  const handleProjectSelect = (project) => {
    setCurrentProject(project);
    setCurrentSession(null); // Clear session when switching projects
  };

  const handleSessionSelect = (session) => {
    setCurrentSession(session);
  };

  return (
    <ProjectProvider>
      <TerminalProvider socket={socket}>
        <div className="flex h-screen bg-gray-900 text-white">
          {/* Sidebar */}
          <div className="w-80 bg-gray-800 border-r border-gray-700">
            <Sidebar
              currentProject={currentProject}
              currentSession={currentSession}
              onProjectSelect={handleProjectSelect}
              onSessionSelect={handleSessionSelect}
              onSettingsClick={() => setShowSettings(true)}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {currentProject && currentSession ? (
              <TerminalView
                project={currentProject}
                session={currentSession}
              />
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">🤖</div>
                  <h1 className="text-2xl font-bold mb-2">Gemini CLI GUI</h1>
                  <p className="text-gray-400 mb-6">
                    Select a project and session to start using Gemini CLI
                  </p>
                  {!currentProject && (
                    <p className="text-sm text-gray-500">
                      Create a new project from the sidebar to get started
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Settings Modal */}
          {showSettings && (
            <SettingsModal
              isOpen={showSettings}
              onClose={() => setShowSettings(false)}
            />
          )}
        </div>
      </TerminalProvider>
    </ProjectProvider>
  );
}

export default App;
