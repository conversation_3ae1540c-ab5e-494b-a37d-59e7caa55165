{"version": 3, "file": "addon-fit.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAkB,SAAID,IAEtBD,EAAe,SAAIC,GACpB,CATD,CASGK,MAAM,I,mHCeT,iBAGS,QAAAC,CAASC,GACdC,KAAKC,UAAYF,CACnB,CAEO,OAAAG,GAAiB,CAEjB,GAAAC,GACL,MAAMC,EAAOJ,KAAKK,oBAClB,IAAKD,IAASJ,KAAKC,WAAaK,MAAMF,EAAKG,OAASD,MAAMF,EAAKI,MAC7D,OAIF,MAAMC,EAAQT,KAAKC,UAAkBS,MAGjCV,KAAKC,UAAUO,OAASJ,EAAKI,MAAQR,KAAKC,UAAUM,OAASH,EAAKG,OACpEE,EAAKE,eAAeC,QACpBZ,KAAKC,UAAUY,OAAOT,EAAKG,KAAMH,EAAKI,MAE1C,CAEO,iBAAAH,GACL,IAAKL,KAAKC,UACR,OAGF,IAAKD,KAAKC,UAAUa,UAAYd,KAAKC,UAAUa,QAAQC,cACrD,OAIF,MAAMN,EAAQT,KAAKC,UAAkBS,MAC/BN,EAA0BK,EAAKE,eAAeK,WAEpD,GAA4B,IAAxBZ,EAAKa,IAAIC,KAAKC,OAAwC,IAAzBf,EAAKa,IAAIC,KAAKE,OAC7C,OAGF,MAAMC,EAAuD,IAAtCrB,KAAKC,UAAUqB,QAAQC,WAC5C,EAAId,EAAKe,SAASC,eAEdC,EAAqBC,OAAOC,iBAAiB5B,KAAKC,UAAUa,QAAQC,eACpEc,EAAsBC,SAASJ,EAAmBK,iBAAiB,WACnEC,EAAqBC,KAAKC,IAAI,EAAGJ,SAASJ,EAAmBK,iBAAiB,WAC9EI,EAAeR,OAAOC,iBAAiB5B,KAAKC,UAAUa,SAStDsB,EAAkBP,GAPjBC,SAASK,EAAaJ,iBAAiB,gBACpCD,SAASK,EAAaJ,iBAAiB,oBAO3CM,EAAiBL,GANdF,SAASK,EAAaJ,iBAAiB,kBACxCD,SAASK,EAAaJ,iBAAiB,kBAKiBV,EAKhE,MAJiB,CACfd,KAAM0B,KAAKC,IA/DI,EA+DcD,KAAKK,MAAMD,EAAiBjC,EAAKa,IAAIC,KAAKC,QACvEX,KAAMyB,KAAKC,IA/DI,EA+DcD,KAAKK,MAAMF,EAAkBhC,EAAKa,IAAIC,KAAKE,SAG5E,E", "sources": ["webpack://FitAddon/webpack/universalModuleDefinition", "webpack://FitAddon/./src/FitAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"FitAddon\"] = factory();\n\telse\n\t\troot[\"FitAddon\"] = factory();\n})(self, () => {\nreturn ", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport type { Terminal, ITerminalAddon } from '@xterm/xterm';\nimport type { FitAddon as IFitApi } from '@xterm/addon-fit';\nimport { IRenderDimensions } from 'browser/renderer/shared/Types';\n\ninterface ITerminalDimensions {\n  /**\n   * The number of rows in the terminal.\n   */\n  rows: number;\n\n  /**\n   * The number of columns in the terminal.\n   */\n  cols: number;\n}\n\nconst MINIMUM_COLS = 2;\nconst MINIMUM_ROWS = 1;\n\nexport class FitAddon implements ITerminalAddon , IFitApi {\n  private _terminal: Terminal | undefined;\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n  }\n\n  public dispose(): void {}\n\n  public fit(): void {\n    const dims = this.proposeDimensions();\n    if (!dims || !this._terminal || isNaN(dims.cols) || isNaN(dims.rows)) {\n      return;\n    }\n\n    // TODO: Remove reliance on private API\n    const core = (this._terminal as any)._core;\n\n    // Force a full render\n    if (this._terminal.rows !== dims.rows || this._terminal.cols !== dims.cols) {\n      core._renderService.clear();\n      this._terminal.resize(dims.cols, dims.rows);\n    }\n  }\n\n  public proposeDimensions(): ITerminalDimensions | undefined {\n    if (!this._terminal) {\n      return undefined;\n    }\n\n    if (!this._terminal.element || !this._terminal.element.parentElement) {\n      return undefined;\n    }\n\n    // TODO: Remove reliance on private API\n    const core = (this._terminal as any)._core;\n    const dims: IRenderDimensions = core._renderService.dimensions;\n\n    if (dims.css.cell.width === 0 || dims.css.cell.height === 0) {\n      return undefined;\n    }\n\n    const scrollbarWidth = this._terminal.options.scrollback === 0 ?\n      0 : core.viewport.scrollBarWidth;\n\n    const parentElementStyle = window.getComputedStyle(this._terminal.element.parentElement);\n    const parentElementHeight = parseInt(parentElementStyle.getPropertyValue('height'));\n    const parentElementWidth = Math.max(0, parseInt(parentElementStyle.getPropertyValue('width')));\n    const elementStyle = window.getComputedStyle(this._terminal.element);\n    const elementPadding = {\n      top: parseInt(elementStyle.getPropertyValue('padding-top')),\n      bottom: parseInt(elementStyle.getPropertyValue('padding-bottom')),\n      right: parseInt(elementStyle.getPropertyValue('padding-right')),\n      left: parseInt(elementStyle.getPropertyValue('padding-left'))\n    };\n    const elementPaddingVer = elementPadding.top + elementPadding.bottom;\n    const elementPaddingHor = elementPadding.right + elementPadding.left;\n    const availableHeight = parentElementHeight - elementPaddingVer;\n    const availableWidth = parentElementWidth - elementPaddingHor - scrollbarWidth;\n    const geometry = {\n      cols: Math.max(MINIMUM_COLS, Math.floor(availableWidth / dims.css.cell.width)),\n      rows: Math.max(MINIMUM_ROWS, Math.floor(availableHeight / dims.css.cell.height))\n    };\n    return geometry;\n  }\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "activate", "terminal", "this", "_terminal", "dispose", "fit", "dims", "proposeDimensions", "isNaN", "cols", "rows", "core", "_core", "_renderService", "clear", "resize", "element", "parentElement", "dimensions", "css", "cell", "width", "height", "scrollbarWidth", "options", "scrollback", "viewport", "scrollBarWidth", "parentElementStyle", "window", "getComputedStyle", "parentElementHeight", "parseInt", "getPropertyValue", "parent<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "max", "elementStyle", "availableHeight", "availableWidth", "floor"], "sourceRoot": ""}