import React, { useState } from 'react';
import { 
  FolderPlus, 
  Settings, 
  ChevronDown, 
  ChevronRight, 
  Play, 
  Trash2,
  Terminal
} from 'lucide-react';
import { useProjects } from '../contexts/ProjectContext';
import { useTerminal } from '../contexts/TerminalContext';
import NewProjectModal from './NewProjectModal';
import NewSessionModal from './NewSessionModal';

const Sidebar = ({ 
  currentProject, 
  currentSession, 
  onProjectSelect, 
  onSessionSelect, 
  onSettingsClick 
}) => {
  const { projects, loading, deleteProject, deleteSession } = useProjects();
  const { getTerminalBySessionId } = useTerminal();
  const [showNewProject, setShowNewProject] = useState(false);
  const [showNewSession, setShowNewSession] = useState(false);
  const [selectedProjectForSession, setSelectedProjectForSession] = useState(null);
  const [expandedProjects, setExpandedProjects] = useState(new Set());

  const toggleProjectExpansion = (projectId) => {
    setExpandedProjects(prev => {
      const newSet = new Set(prev);
      if (newSet.has(projectId)) {
        newSet.delete(projectId);
      } else {
        newSet.add(projectId);
      }
      return newSet;
    });
  };

  const handleNewSession = (project) => {
    setSelectedProjectForSession(project);
    setShowNewSession(true);
  };

  const handleDeleteProject = async (project, e) => {
    e.stopPropagation();
    if (window.confirm(`Are you sure you want to delete project "${project.name}"?`)) {
      try {
        await deleteProject(project.id);
        if (currentProject?.id === project.id) {
          onProjectSelect(null);
        }
      } catch (error) {
        alert(`Failed to delete project: ${error.message}`);
      }
    }
  };

  const handleDeleteSession = async (project, session, e) => {
    e.stopPropagation();
    if (window.confirm(`Are you sure you want to delete session "${session.name}"?\n\nThis will kill any running terminal session and remove the session permanently.`)) {
      try {
        await deleteSession(project.id, session.id);
        // If this was the current session, clear it
        if (currentSession?.id === session.id) {
          onSessionSelect(null);
        }
      } catch (error) {
        alert(`Failed to delete session: ${error.message}`);
      }
    }
  };

  const getSessionStatus = (session) => {
    const terminal = getTerminalBySessionId(session.id);
    if (terminal) {
      return terminal.status;
    }
    return 'stopped';
  };

  const getSessionStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'text-green-400';
      case 'starting':
        return 'text-yellow-400';
      case 'exited':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-700 rounded mb-4"></div>
          <div className="h-4 bg-gray-700 rounded mb-2"></div>
          <div className="h-4 bg-gray-700 rounded mb-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Projects & Sessions</h2>
          <button
            onClick={onSettingsClick}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            title="Settings"
          >
            <Settings size={18} />
          </button>
        </div>
        
        <button
          onClick={() => setShowNewProject(true)}
          className="w-full flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
        >
          <FolderPlus size={18} />
          New Project
        </button>
      </div>

      {/* Projects List */}
      <div className="flex-1 overflow-y-auto">
        {projects.length === 0 ? (
          <div className="p-4 text-center text-gray-400">
            <p>No projects yet</p>
            <p className="text-sm mt-1">Create your first project to get started</p>
          </div>
        ) : (
          <div className="p-2">
            {projects.map((project) => (
              <div key={project.id} className="mb-2">
                {/* Project Header */}
                <div
                  className={`flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-colors ${
                    currentProject?.id === project.id
                      ? 'bg-blue-600 text-white'
                      : 'hover:bg-gray-700'
                  }`}
                  onClick={() => {
                    onProjectSelect(project);
                    toggleProjectExpansion(project.id);
                  }}
                >
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleProjectExpansion(project.id);
                    }}
                    className="p-1"
                  >
                    {expandedProjects.has(project.id) ? (
                      <ChevronDown size={16} />
                    ) : (
                      <ChevronRight size={16} />
                    )}
                  </button>
                  
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{project.name}</div>
                    <div className="text-xs text-gray-400 truncate">
                      {project.path}
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleNewSession(project);
                      }}
                      className="p-1 hover:bg-gray-600 rounded transition-colors"
                      title="New Session"
                    >
                      <Play size={14} />
                    </button>
                    <button
                      onClick={(e) => handleDeleteProject(project, e)}
                      className="p-1 hover:bg-red-600 rounded transition-colors"
                      title="Delete Project"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                </div>

                {/* Sessions List */}
                {expandedProjects.has(project.id) && (
                  <div className="ml-6 mt-1 space-y-1">
                    {project.sessions.length === 0 ? (
                      <div className="text-sm text-gray-500 p-2">
                        No sessions yet
                      </div>
                    ) : (
                      project.sessions.map((session) => {
                        const status = getSessionStatus(session);
                        return (
                          <div
                            key={session.id}
                            className={`flex items-center gap-2 p-2 rounded cursor-pointer transition-colors group ${
                              currentSession?.id === session.id
                                ? 'bg-blue-500 text-white'
                                : 'hover:bg-gray-700'
                            }`}
                            onClick={() => onSessionSelect(session)}
                          >
                            <Terminal size={14} className={getSessionStatusColor(status)} />
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-medium truncate">
                                {session.name}
                              </div>
                              <div className="text-xs text-gray-400">
                                {status}
                              </div>
                            </div>
                            <button
                              onClick={(e) => handleDeleteSession(project, session, e)}
                              className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-600 rounded transition-all"
                              title="Delete Session"
                            >
                              <Trash2 size={12} />
                            </button>
                          </div>
                        );
                      })
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      {showNewProject && (
        <NewProjectModal
          isOpen={showNewProject}
          onClose={() => setShowNewProject(false)}
        />
      )}

      {showNewSession && selectedProjectForSession && (
        <NewSessionModal
          isOpen={showNewSession}
          project={selectedProjectForSession}
          onClose={() => {
            setShowNewSession(false);
            setSelectedProjectForSession(null);
          }}
        />
      )}
    </div>
  );
};

export default Sidebar;
