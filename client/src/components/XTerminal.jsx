import React, { useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import '@xterm/xterm/css/xterm.css';

const XTerminal = forwardRef(({ output, onInput, disabled = false }, ref) => {
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);
  const resizeObserverRef = useRef(null);

  useEffect(() => {
    if (!terminalRef.current) return;

    // Create terminal instance
    const terminal = new Terminal({
      theme: {
        background: '#1a1a1a',
        foreground: '#ffffff',
        cursor: '#ffffff',
        selection: '#ffffff30',
        black: '#000000',
        red: '#ff5555',
        green: '#50fa7b',
        yellow: '#f1fa8c',
        blue: '#bd93f9',
        magenta: '#ff79c6',
        cyan: '#8be9fd',
        white: '#bfbfbf',
        brightBlack: '#4d4d4d',
        brightRed: '#ff6e67',
        brightGreen: '#5af78e',
        brightYellow: '#f4f99d',
        brightBlue: '#caa9fa',
        brightMagenta: '#ff92d0',
        brightCyan: '#9aedfe',
        brightWhite: '#e6e6e6'
      },
      fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
      fontSize: 14,
      lineHeight: 1.2,
      cursorBlink: true,
      cursorStyle: 'block',
      scrollback: 10000, // Increased scrollback
      convertEol: true,
      disableStdin: disabled,
      allowProposedApi: true, // Enable proposed APIs for better features
      scrollOnUserInput: true,
      fastScrollModifier: 'alt'
    });

    // Create fit addon
    const fitAddon = new FitAddon();
    terminal.loadAddon(fitAddon);

    // Open terminal
    terminal.open(terminalRef.current);

    // Store references first
    xtermRef.current = terminal;
    fitAddonRef.current = fitAddon;

    // Fit after a small delay to ensure DOM is ready
    setTimeout(() => {
      try {
        fitAddon.fit();
      } catch (error) {
        console.warn('Failed to fit terminal:', error);
      }
    }, 100);

    // Handle input
    terminal.onData((data) => {
      console.log('Terminal input:', data, 'disabled:', disabled);
      if (!disabled && onInput) {
        onInput(data);
      }
    });

    // Handle resize with debouncing
    const handleResize = () => {
      if (fitAddonRef.current && xtermRef.current) {
        try {
          // Small delay to ensure DOM has updated
          setTimeout(() => {
            if (fitAddonRef.current && xtermRef.current) {
              fitAddonRef.current.fit();
            }
          }, 10);
        } catch (error) {
          console.warn('Failed to resize terminal:', error);
        }
      }
    };

    // Listen for window resize
    window.addEventListener('resize', handleResize);

    // Use ResizeObserver to detect container size changes
    if (terminalRef.current && window.ResizeObserver) {
      resizeObserverRef.current = new ResizeObserver((entries) => {
        for (const entry of entries) {
          // Debounce resize calls
          clearTimeout(resizeObserverRef.current.timeout);
          resizeObserverRef.current.timeout = setTimeout(handleResize, 50);
        }
      });

      resizeObserverRef.current.observe(terminalRef.current);
    }

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      // Cleanup ResizeObserver
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        if (resizeObserverRef.current.timeout) {
          clearTimeout(resizeObserverRef.current.timeout);
        }
      }

      // Dispose terminal
      if (xtermRef.current) {
        xtermRef.current.dispose();
      }
    };
  }, []);

  // Handle output updates
  const lastOutputRef = useRef('');

  // Expose fit method for manual triggering
  const triggerFit = () => {
    if (fitAddonRef.current && xtermRef.current) {
      try {
        fitAddonRef.current.fit();
      } catch (error) {
        console.warn('Failed to manually fit terminal:', error);
      }
    }
  };

  // Expose refresh method for manual triggering
  const triggerRefresh = () => {
    if (xtermRef.current) {
      try {
        // Refresh the entire terminal content
        xtermRef.current.refresh(0, xtermRef.current.rows - 1);
      } catch (error) {
        console.warn('Failed to manually refresh terminal:', error);
      }
    }
  };

  useEffect(() => {
    if (xtermRef.current && output) {
      // Only write new content to avoid flickering
      const newContent = output.slice(lastOutputRef.current.length);
      if (newContent) {
        xtermRef.current.write(newContent);
        lastOutputRef.current = output;

        // Trigger fit and refresh after new content to ensure proper sizing and visibility
        setTimeout(() => {
          triggerFit();
          triggerRefresh();
        }, 10);
      }
    }
  }, [output]);

  // Add effect to handle visibility changes and refresh terminal content
  useEffect(() => {
    if (!xtermRef.current || !terminalRef.current) return;

    // Use IntersectionObserver to detect when terminal becomes visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.intersectionRatio > 0) {
            // Terminal became visible, refresh content and fit
            setTimeout(() => {
              triggerRefresh();
              triggerFit();
            }, 50); // Small delay to ensure DOM is ready
          }
        });
      },
      {
        threshold: 0.1, // Trigger when at least 10% of terminal is visible
        rootMargin: '10px' // Add some margin for better detection
      }
    );

    observer.observe(terminalRef.current);

    return () => {
      observer.disconnect();
    };
  }, [output]); // Re-run when output changes to ensure we have content to refresh

  // Handle disabled state
  useEffect(() => {
    if (xtermRef.current) {
      xtermRef.current.options.disableStdin = disabled;
    }
  }, [disabled]);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    refresh: triggerRefresh,
    fit: triggerFit,
    focus: () => {
      if (xtermRef.current) {
        xtermRef.current.focus();
      }
    }
  }), []);

  return (
    <div
      ref={terminalRef}
      className="w-full h-full overflow-hidden"
      style={{
        minHeight: '200px',
        // Ensure the terminal container can be properly measured
        position: 'relative'
      }}
    />
  );
});

XTerminal.displayName = 'XTerminal';

export default XTerminal;
