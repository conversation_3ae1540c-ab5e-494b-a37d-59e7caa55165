import React, { useEffect, useRef, useImperativeHandle, forwardRef, useState } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import '@xterm/xterm/css/xterm.css';

const XTerminal = forwardRef(({ output, onInput, disabled = false, sessionId, isVisible = true }, ref) => {
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);
  const resizeObserverRef = useRef(null);
  const lastSessionIdRef = useRef(null);
  const lastOutputRef = useRef('');
  const isInitializedRef = useRef(false);
  const [containerHeight, setContainerHeight] = useState(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    forceRefresh: () => {
      console.log(`[XTerminal] Force refresh called via ref for session ${sessionId}`);
      if (xtermRef.current && fitAddonRef.current) {
        try {
          // Clear and rewrite all content
          xtermRef.current.clear();

          // Rewrite the output if we have it
          if (output) {
            console.log(`[XTerminal] Rewriting output via ref, length: ${output.length}`);
            xtermRef.current.write(output);
            lastOutputRef.current = output;
          }

          // Force fit and refresh
          setTimeout(() => {
            if (fitAddonRef.current && xtermRef.current) {
              fitAddonRef.current.fit();
              xtermRef.current.refresh(0, xtermRef.current.rows - 1);
              xtermRef.current.scrollToBottom();
              console.log(`[XTerminal] Force refresh via ref completed`);
            }
          }, 50);

        } catch (error) {
          console.error('Failed to force refresh terminal via ref:', error);
        }
      } else {
        console.warn(`[XTerminal] Cannot force refresh - terminal not initialized`);
      }
    }
  }), [sessionId, output]);

  // Initialize terminal
  const initializeTerminal = () => {
    if (!terminalRef.current || xtermRef.current) return;

    console.log(`[XTerminal] Initializing terminal for session ${sessionId}`);

    // Create terminal instance
    const terminal = new Terminal({
      theme: {
        background: '#1a1a1a',
        foreground: '#ffffff',
        cursor: '#ffffff',
        selection: '#ffffff30',
        black: '#000000',
        red: '#ff5555',
        green: '#50fa7b',
        yellow: '#f1fa8c',
        blue: '#bd93f9',
        magenta: '#ff79c6',
        cyan: '#8be9fd',
        white: '#bfbfbf',
        brightBlack: '#4d4d4d',
        brightRed: '#ff6e67',
        brightGreen: '#5af78e',
        brightYellow: '#f4f99d',
        brightBlue: '#caa9fa',
        brightMagenta: '#ff92d0',
        brightCyan: '#9aedfe',
        brightWhite: '#e6e6e6'
      },
      fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
      fontSize: 14,
      lineHeight: 1.2,
      cursorBlink: true,
      cursorStyle: 'block',
      // Set explicit initial dimensions - FitAddon will adjust these to fit container
      rows: 30,
      cols: 180,
      scrollback: 10000,
      convertEol: true,
      disableStdin: disabled,
      allowProposedApi: true,
      scrollOnUserInput: true,
      fastScrollModifier: 'alt'
    });

    // Create fit addon
    const fitAddon = new FitAddon();
    terminal.loadAddon(fitAddon);

    // Open terminal
    terminal.open(terminalRef.current);

    // Store references
    xtermRef.current = terminal;
    fitAddonRef.current = fitAddon;
    isInitializedRef.current = true;

    // Handle input
    terminal.onData((data) => {
      console.log('Terminal input:', data, 'disabled:', disabled);
      if (!disabled && onInput) {
        onInput(data);
      }
    });

    // Fit after initialization with better timing and debugging
    setTimeout(() => {
      try {
        if (fitAddon && terminal && terminalRef.current) {
          fitAddon.fit();

          // Debug: Log actual dimensions after fit
          const containerRect = terminalRef.current.getBoundingClientRect();
          const terminalCols = terminal.cols;
          const terminalRows = terminal.rows;

          console.log(`[XTerminal] Terminal fitted for session ${sessionId}:`);
          console.log(`  Container: ${containerRect.width}x${containerRect.height}px`);
          console.log(`  Terminal: ${terminalCols}x${terminalRows} chars`);

          // Try to get cell dimensions if available
          try {
            const cellHeight = terminal._core?._renderService?.dimensions?.css?.cell?.height;
            const cellWidth = terminal._core?._renderService?.dimensions?.css?.cell?.width;
            if (cellHeight && cellWidth) {
              console.log(`  Cell size: ${cellWidth}x${cellHeight}px`);
              console.log(`  Expected terminal size: ${terminalCols * cellWidth}x${terminalRows * cellHeight}px`);

              // Calculate optimal container height based on character grid
              const optimalHeight = terminalRows * cellHeight;
              setContainerHeight(optimalHeight);
              console.log(`  Setting container height to: ${optimalHeight}px`);
            }
          } catch (e) {
            console.log('  Could not access cell dimensions');
          }
        }
      } catch (error) {
        console.warn('Failed to fit terminal:', error);
      }
    }, 100);

    return terminal;
  };

  useEffect(() => {
    if (!terminalRef.current) return;

    initializeTerminal();

    // Handle resize with debouncing
    const handleResize = () => {
      if (fitAddonRef.current && xtermRef.current) {
        try {
          // Small delay to ensure DOM has updated
          setTimeout(() => {
            if (fitAddonRef.current && xtermRef.current) {
              fitAddonRef.current.fit();
            }
          }, 10);
        } catch (error) {
          console.warn('Failed to resize terminal:', error);
        }
      }
    };

    // Listen for window resize
    window.addEventListener('resize', handleResize);

    // Use ResizeObserver to detect container size changes
    if (terminalRef.current && window.ResizeObserver) {
      resizeObserverRef.current = new ResizeObserver(() => {
        // Debounce resize calls
        clearTimeout(resizeObserverRef.current.timeout);
        resizeObserverRef.current.timeout = setTimeout(handleResize, 50);
      });

      resizeObserverRef.current.observe(terminalRef.current);
    }

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      // Cleanup ResizeObserver
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        if (resizeObserverRef.current.timeout) {
          clearTimeout(resizeObserverRef.current.timeout);
        }
      }

      // Dispose terminal
      if (xtermRef.current) {
        xtermRef.current.dispose();
        xtermRef.current = null;
        fitAddonRef.current = null;
        isInitializedRef.current = false;
      }
    };
  }, []);

  // Handle session changes - clear terminal and reset when session changes
  useEffect(() => {
    if (sessionId !== lastSessionIdRef.current) {
      console.log(`[XTerminal] Session changed from ${lastSessionIdRef.current} to ${sessionId}`);

      // Clear terminal content when session changes
      if (xtermRef.current) {
        xtermRef.current.clear();
        console.log(`[XTerminal] Cleared terminal for session switch`);
      }

      // Reset output tracking
      lastOutputRef.current = '';
      lastSessionIdRef.current = sessionId;
    }
  }, [sessionId]);

  // Handle visibility changes - force refresh when terminal becomes visible
  // DISABLED FOR TESTING: This was triggering on every output change due to [isVisible, output] dependency
  // causing refresh cascades. Testing if we need this at all.
  /*
  useEffect(() => {
    if (isVisible && xtermRef.current && fitAddonRef.current) {
      console.log(`[XTerminal] Terminal became visible, using same approach as refresh button`);

      // Use the same successful approach as the manual refresh button
      setTimeout(() => {
        try {
          if (fitAddonRef.current && xtermRef.current) {
            // Clear and rewrite all content (same as refresh button)
            xtermRef.current.clear();

            // Rewrite the output if we have it
            if (output) {
              console.log(`[XTerminal] Auto-rewriting output on visibility, length: ${output.length}`);
              xtermRef.current.write(output);
              lastOutputRef.current = output;
            }

            // Force fit and refresh (same as refresh button)
            fitAddonRef.current.fit();
            xtermRef.current.refresh(0, xtermRef.current.rows - 1);
            xtermRef.current.scrollToBottom();
            console.log(`[XTerminal] Auto-refresh on visibility completed`);
          }
        } catch (error) {
          console.warn('Failed to refresh terminal on visibility change:', error);
        }
      }, 100);
    }
  }, [isVisible, output]);
  */

  // Listen for manual refresh events
  useEffect(() => {
    const handleForceRefresh = (event) => {
      if (event.detail.sessionId === sessionId && xtermRef.current && fitAddonRef.current) {
        console.log(`[XTerminal] Manual refresh requested for session ${sessionId}`);

        try {
          // Clear and rewrite all content
          xtermRef.current.clear();

          // Rewrite the output if we have it
          if (output) {
            console.log(`[XTerminal] Rewriting output, length: ${output.length}`);
            xtermRef.current.write(output);
            lastOutputRef.current = output;
          }

          // Force fit and refresh
          setTimeout(() => {
            if (fitAddonRef.current && xtermRef.current) {
              fitAddonRef.current.fit();
              xtermRef.current.refresh(0, xtermRef.current.rows - 1);
              xtermRef.current.scrollToBottom();
              console.log(`[XTerminal] Manual refresh completed`);
            }
          }, 50);

        } catch (error) {
          console.error('Failed to manually refresh terminal:', error);
        }
      }
    };

    window.addEventListener('forceTerminalRefresh', handleForceRefresh);
    return () => window.removeEventListener('forceTerminalRefresh', handleForceRefresh);
  }, [sessionId, output]);

  // Expose fit method for manual triggering
  const triggerFit = () => {
    if (fitAddonRef.current && xtermRef.current) {
      try {
        fitAddonRef.current.fit();
      } catch (error) {
        console.warn('Failed to manually fit terminal:', error);
      }
    }
  };

  // Handle output updates
  useEffect(() => {
    if (!xtermRef.current || !output) return;

    // For session changes, write all content
    if (lastOutputRef.current === '' && output) {
      console.log(`[XTerminal] Writing full output for session ${sessionId}, length: ${output.length}`);
      xtermRef.current.write(output);
      lastOutputRef.current = output;

      // Trigger fit after writing content
      setTimeout(triggerFit, 10);
    }
    // For updates, only write new content
    else if (output.length > lastOutputRef.current.length) {
      const newContent = output.slice(lastOutputRef.current.length);
      console.log(`[XTerminal] Writing new content, length: ${newContent.length}`);
      xtermRef.current.write(newContent);
      lastOutputRef.current = output;

      // Trigger fit after new content
      setTimeout(triggerFit, 10);
    }
  }, [output, sessionId]);

  // Handle disabled state
  useEffect(() => {
    if (xtermRef.current) {
      xtermRef.current.options.disableStdin = disabled;
    }
  }, [disabled]);

  return (
    <div
      ref={terminalRef}
      className="w-full overflow-hidden"
      style={{
        // Use calculated height if available, otherwise fall back to flexbox
        height: containerHeight ? `${containerHeight}px` : '100%',
        // Ensure the terminal container can be properly measured
        position: 'relative'
      }}
    />
  );
});

XTerminal.displayName = 'XTerminal';

export default XTerminal;
