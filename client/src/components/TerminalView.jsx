import React, { useState, useEffect, useRef } from 'react';
import { Play, Square, RotateCcw, Send } from 'lucide-react';
import { useTerminal } from '../contexts/TerminalContext';
import XTerminal from './XTerminal';

const TerminalView = ({ project, session }) => {
  const {
    startTerminal,
    sendInput,
    killTerminal,
    getTerminalBySessionId,
    clearTerminalOutput
  } = useTerminal();

  const [isStarting, setIsStarting] = useState(false);
  const [error, setError] = useState('');
  const [inputValue, setInputValue] = useState('');
  const terminalRef = useRef(null);

  const terminal = getTerminalBySessionId(session.id);

  // Effect to refresh terminal when session changes
  useEffect(() => {
    if (terminal && terminalRef.current) {
      // Small delay to ensure the terminal is fully mounted and visible
      const timer = setTimeout(() => {
        if (terminalRef.current) {
          terminalRef.current.refresh();
          terminalRef.current.fit();
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [session.id, terminal]); // Re-run when session or terminal changes

  // Handle terminal input from xterm
  const handleTerminalInput = async (data) => {
    console.log('TerminalView handleTerminalInput:', data, 'terminal:', terminal);
    if (!terminal) {
      console.log('No terminal found for input');
      return;
    }

    try {
      console.log('Sending input to terminal:', terminal.id);
      await sendInput(terminal.id, data);
    } catch (err) {
      console.error('Error sending input:', err);
      setError(err.message);
    }
  };

  // Handle input from our custom input field
  const handleSendMessage = async (e) => {
    e.preventDefault();

    if (!inputValue.trim() || !terminal) return;

    try {
      // Send the message - the backend will handle adding Enter
      await sendInput(terminal.id, inputValue);
      setInputValue('');
    } catch (err) {
      setError(err.message);
    }
  };

  const handleKeyDown = (e) => {
    // Handle Cmd/Ctrl+Enter for sending messages
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSendMessage(e);
      return;
    }

    // Handle arrow keys and plain Enter for menu navigation
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Enter'].includes(e.key)) {
      e.preventDefault();
      handleNavigationKey(e.key);
    }
  };

  const handleNavigationKey = async (key) => {
    if (!terminal) return;

    try {
      // Send the navigation key directly to the terminal
      let keyToSend;
      switch (key) {
        case 'ArrowUp':
          keyToSend = '\x1b[A'; // ANSI escape sequence for up arrow
          break;
        case 'ArrowDown':
          keyToSend = '\x1b[B'; // ANSI escape sequence for down arrow
          break;
        case 'ArrowLeft':
          keyToSend = '\x1b[D'; // ANSI escape sequence for left arrow
          break;
        case 'ArrowRight':
          keyToSend = '\x1b[C'; // ANSI escape sequence for right arrow
          break;
        case 'Enter':
          keyToSend = '\r'; // Plain Enter for menu selection
          break;
        default:
          return;
      }

      await sendInput(terminal.id, keyToSend);
    } catch (err) {
      setError(err.message);
    }
  };



  const handleStartSession = async () => {
    setIsStarting(true);
    setError('');

    try {
      await startTerminal(
        project.id,
        session.id,
        project.path,
        session.name,
        session.prompt
      );
    } catch (err) {
      setError(err.message);
    } finally {
      setIsStarting(false);
    }
  };

  const handleStopSession = async () => {
    if (terminal) {
      try {
        await killTerminal(terminal.id);
      } catch (err) {
        setError(err.message);
      }
    }
  };



  const handleClearTerminal = () => {
    if (terminal) {
      clearTerminalOutput(terminal.id);
    }
  };



  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'text-green-400';
      case 'starting':
        return 'text-yellow-400';
      case 'exited':
        return 'text-red-400';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusText = (terminal) => {
    if (!terminal) return 'Not started';

    switch (terminal.status) {
      case 'running':
        return 'Ready';
      case 'starting':
        return 'Starting Gemini CLI...';
      case 'exited':
        return `Exited (code: ${terminal.exitCode || 'unknown'})`;
      case 'error':
        return `Error: ${terminal.error || 'Unknown error'}`;
      default:
        return terminal.status || 'Unknown';
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">{project.name}</h1>
            <p className="text-sm text-gray-400">{project.path}</p>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm font-medium">{session.name}</span>
              <span className={`text-xs ${getStatusColor(terminal?.status)}`}>
                • {getStatusText(terminal)}
              </span>
              <span className="text-xs text-yellow-400">
                (Debug: {terminal?.status || 'no terminal'})
              </span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {!terminal ? (
              <button
                onClick={handleStartSession}
                disabled={isStarting}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 rounded-lg transition-colors"
              >
                <Play size={16} />
                {isStarting ? 'Starting...' : 'Start Session'}
              </button>
            ) : (
              <>
                <button
                  onClick={handleClearTerminal}
                  className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                  title="Clear terminal"
                >
                  <RotateCcw size={16} />
                </button>
                <button
                  onClick={handleStopSession}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                >
                  <Square size={16} />
                  Stop
                </button>
              </>
            )}
          </div>
        </div>

        {error && (
          <div className="mt-3 p-3 bg-red-900 border border-red-700 rounded-lg text-red-200 text-sm">
            {error}
          </div>
        )}

        {terminal && terminal.status === 'error' && (
          <div className="mt-3 p-3 bg-red-900 border border-red-700 rounded-lg text-red-200 text-sm">
            <strong>Terminal Error:</strong> {terminal.error}
          </div>
        )}

        {terminal && terminal.status === 'exited' && (
          <div className="mt-3 p-3 bg-yellow-900 border border-yellow-700 rounded-lg text-yellow-200 text-sm">
            <strong>Session Exited:</strong> The terminal session exited with code {terminal.exitCode || 'unknown'}.
            You can try starting a new session.
          </div>
        )}
      </div>

      {/* Terminal Output */}
      <div className="flex-1 flex flex-col bg-terminal-bg">
        {!terminal ? (
          <div className="flex-1 flex items-center justify-center text-gray-400">
            <div className="text-center">
              <div className="text-4xl mb-4">⚡</div>
              <p>Click "Start Session" to begin using Gemini CLI</p>
              {session.prompt && (
                <p className="text-sm mt-2 text-gray-500">
                  Initial prompt: "{session.prompt}"
                </p>
              )}
            </div>
          </div>
        ) : (
          <>
            {/* Terminal Output Area */}
            <div className="flex-1 flex flex-col min-h-0 p-4">
              <div className="flex-1 min-h-0">
                <XTerminal
                  ref={terminalRef}
                  output={terminal.output || ''}
                  onInput={null}
                  disabled={true}
                />
              </div>
            </div>

            {/* Input Area */}
            <div className="border-t border-gray-700 p-4">
              <form onSubmit={handleSendMessage} className="flex gap-2">
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Type message (⌘↵ to send) or use ↑↓ arrows + Enter for menus"
                  className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                  disabled={terminal.status !== 'running'}
                  autoFocus
                />
                <button
                  type="submit"
                  disabled={!inputValue.trim() || terminal.status !== 'running'}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-lg transition-colors flex items-center gap-2"
                >
                  <Send size={16} />
                </button>
              </form>
              <p className="text-xs text-gray-500 mt-2">
                ⌘↵/Ctrl+Enter: Send message • ↑↓←→: Navigate menus • Enter: Select menu item
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TerminalView;
