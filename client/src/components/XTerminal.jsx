import React, { useEffect, useRef } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import '@xterm/xterm/css/xterm.css';

const XTerminal = ({ output, onInput, disabled = false, sessionId, isVisible = true }) => {
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);
  const resizeObserverRef = useRef(null);
  const lastSessionIdRef = useRef(null);
  const lastOutputRef = useRef('');
  const isInitializedRef = useRef(false);

  // Initialize terminal
  const initializeTerminal = () => {
    if (!terminalRef.current || xtermRef.current) return;

    console.log(`[XTerminal] Initializing terminal for session ${sessionId}`);

    // Create terminal instance
    const terminal = new Terminal({
      theme: {
        background: '#1a1a1a',
        foreground: '#ffffff',
        cursor: '#ffffff',
        selection: '#ffffff30',
        black: '#000000',
        red: '#ff5555',
        green: '#50fa7b',
        yellow: '#f1fa8c',
        blue: '#bd93f9',
        magenta: '#ff79c6',
        cyan: '#8be9fd',
        white: '#bfbfbf',
        brightBlack: '#4d4d4d',
        brightRed: '#ff6e67',
        brightGreen: '#5af78e',
        brightYellow: '#f4f99d',
        brightBlue: '#caa9fa',
        brightMagenta: '#ff92d0',
        brightCyan: '#9aedfe',
        brightWhite: '#e6e6e6'
      },
      fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
      fontSize: 14,
      lineHeight: 1.2,
      cursorBlink: true,
      cursorStyle: 'block',
      scrollback: 10000,
      convertEol: true,
      disableStdin: disabled,
      allowProposedApi: true,
      scrollOnUserInput: true,
      fastScrollModifier: 'alt'
    });

    // Create fit addon
    const fitAddon = new FitAddon();
    terminal.loadAddon(fitAddon);

    // Open terminal
    terminal.open(terminalRef.current);

    // Store references
    xtermRef.current = terminal;
    fitAddonRef.current = fitAddon;
    isInitializedRef.current = true;

    // Handle input
    terminal.onData((data) => {
      console.log('Terminal input:', data, 'disabled:', disabled);
      if (!disabled && onInput) {
        onInput(data);
      }
    });

    // Fit after initialization
    setTimeout(() => {
      try {
        if (fitAddon && terminal) {
          fitAddon.fit();
          console.log(`[XTerminal] Terminal fitted for session ${sessionId}`);
        }
      } catch (error) {
        console.warn('Failed to fit terminal:', error);
      }
    }, 100);

    return terminal;
  };

  useEffect(() => {
    if (!terminalRef.current) return;

    initializeTerminal();

    // Handle resize with debouncing
    const handleResize = () => {
      if (fitAddonRef.current && xtermRef.current) {
        try {
          // Small delay to ensure DOM has updated
          setTimeout(() => {
            if (fitAddonRef.current && xtermRef.current) {
              fitAddonRef.current.fit();
            }
          }, 10);
        } catch (error) {
          console.warn('Failed to resize terminal:', error);
        }
      }
    };

    // Listen for window resize
    window.addEventListener('resize', handleResize);

    // Use ResizeObserver to detect container size changes
    if (terminalRef.current && window.ResizeObserver) {
      resizeObserverRef.current = new ResizeObserver((entries) => {
        for (const entry of entries) {
          // Debounce resize calls
          clearTimeout(resizeObserverRef.current.timeout);
          resizeObserverRef.current.timeout = setTimeout(handleResize, 50);
        }
      });

      resizeObserverRef.current.observe(terminalRef.current);
    }

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      // Cleanup ResizeObserver
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        if (resizeObserverRef.current.timeout) {
          clearTimeout(resizeObserverRef.current.timeout);
        }
      }

      // Dispose terminal
      if (xtermRef.current) {
        xtermRef.current.dispose();
        xtermRef.current = null;
        fitAddonRef.current = null;
        isInitializedRef.current = false;
      }
    };
  }, []);

  // Handle session changes - clear terminal and reset when session changes
  useEffect(() => {
    if (sessionId !== lastSessionIdRef.current) {
      console.log(`[XTerminal] Session changed from ${lastSessionIdRef.current} to ${sessionId}`);

      // Clear terminal content when session changes
      if (xtermRef.current) {
        xtermRef.current.clear();
        console.log(`[XTerminal] Cleared terminal for session switch`);
      }

      // Reset output tracking
      lastOutputRef.current = '';
      lastSessionIdRef.current = sessionId;
    }
  }, [sessionId]);

  // Handle visibility changes - force refresh when terminal becomes visible
  useEffect(() => {
    if (isVisible && xtermRef.current && fitAddonRef.current) {
      console.log(`[XTerminal] Terminal became visible, forcing refresh`);

      // Force a refresh when terminal becomes visible
      setTimeout(() => {
        try {
          if (fitAddonRef.current && xtermRef.current) {
            fitAddonRef.current.fit();

            // Force a render refresh
            xtermRef.current.refresh(0, xtermRef.current.rows - 1);
            console.log(`[XTerminal] Forced refresh completed`);
          }
        } catch (error) {
          console.warn('Failed to refresh terminal on visibility change:', error);
        }
      }, 100);
    }
  }, [isVisible]);

  // Expose fit method for manual triggering
  const triggerFit = () => {
    if (fitAddonRef.current && xtermRef.current) {
      try {
        fitAddonRef.current.fit();
      } catch (error) {
        console.warn('Failed to manually fit terminal:', error);
      }
    }
  };

  // Handle output updates
  useEffect(() => {
    if (!xtermRef.current || !output) return;

    // For session changes, write all content
    if (lastOutputRef.current === '' && output) {
      console.log(`[XTerminal] Writing full output for session ${sessionId}, length: ${output.length}`);
      xtermRef.current.write(output);
      lastOutputRef.current = output;

      // Trigger fit after writing content
      setTimeout(triggerFit, 10);
    }
    // For updates, only write new content
    else if (output.length > lastOutputRef.current.length) {
      const newContent = output.slice(lastOutputRef.current.length);
      console.log(`[XTerminal] Writing new content, length: ${newContent.length}`);
      xtermRef.current.write(newContent);
      lastOutputRef.current = output;

      // Trigger fit after new content
      setTimeout(triggerFit, 10);
    }
  }, [output, sessionId]);

  // Handle disabled state
  useEffect(() => {
    if (xtermRef.current) {
      xtermRef.current.options.disableStdin = disabled;
    }
  }, [disabled]);

  return (
    <div
      ref={terminalRef}
      className="w-full h-full overflow-hidden"
      style={{
        minHeight: '200px',
        // Ensure the terminal container can be properly measured
        position: 'relative'
      }}
    />
  );
};

export default XTerminal;
