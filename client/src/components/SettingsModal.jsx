import React, { useState, useEffect } from 'react';
import { X, Save, RotateCcw, FileText, Settings as SettingsIcon } from 'lucide-react';
import Editor from '@monaco-editor/react';

const SettingsModal = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('settings');
  const [settingsContent, setSettingsContent] = useState('');
  const [geminiMdContent, setGeminiMdContent] = useState('');
  const [originalSettings, setOriginalSettings] = useState('');
  const [originalGeminiMd, setOriginalGeminiMd] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [validationError, setValidationError] = useState('');

  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  const loadData = async () => {
    setLoading(true);
    setError('');

    try {
      // Load settings
      const settingsResponse = await fetch('/api/settings');
      if (!settingsResponse.ok) {
        throw new Error('Failed to load settings');
      }
      const settings = await settingsResponse.json();
      const settingsJson = JSON.stringify(settings, null, 2);
      setSettingsContent(settingsJson);
      setOriginalSettings(settingsJson);

      // Load GEMINI.md
      const geminiMdResponse = await fetch('/api/gemini-md');
      if (!geminiMdResponse.ok) {
        throw new Error('Failed to load GEMINI.md');
      }
      const geminiMdData = await geminiMdResponse.json();
      setGeminiMdContent(geminiMdData.content || '');
      setOriginalGeminiMd(geminiMdData.content || '');
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const validateSettings = async (jsonString) => {
    try {
      const response = await fetch('/api/settings/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ jsonString }),
      });

      const result = await response.json();
      return result;
    } catch (err) {
      return { valid: false, error: 'Failed to validate settings' };
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    setError('');
    setValidationError('');

    try {
      // Validate JSON first
      const validation = await validateSettings(settingsContent);
      if (!validation.valid) {
        setValidationError(validation.error);
        setSaving(false);
        return;
      }

      // Save settings
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validation.parsed),
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      setOriginalSettings(settingsContent);
      alert('Settings saved successfully!');
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleSaveGeminiMd = async () => {
    setSaving(true);
    setError('');

    try {
      const response = await fetch('/api/gemini-md', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: geminiMdContent }),
      });

      if (!response.ok) {
        throw new Error('Failed to save GEMINI.md');
      }

      setOriginalGeminiMd(geminiMdContent);
      alert('GEMINI.md saved successfully!');
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleResetSettings = () => {
    if (window.confirm('Are you sure you want to reset your changes?')) {
      setSettingsContent(originalSettings);
      setValidationError('');
    }
  };

  const handleResetGeminiMd = () => {
    if (window.confirm('Are you sure you want to reset your changes?')) {
      setGeminiMdContent(originalGeminiMd);
    }
  };

  const hasSettingsChanges = settingsContent !== originalSettings;
  const hasGeminiMdChanges = geminiMdContent !== originalGeminiMd;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-4xl h-5/6 mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold">Settings</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700">
          <button
            onClick={() => setActiveTab('settings')}
            className={`px-6 py-3 font-medium transition-colors flex items-center gap-2 ${
              activeTab === 'settings'
                ? 'text-blue-400 border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <SettingsIcon size={16} />
            settings.json
            {hasSettingsChanges && <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>}
          </button>
          <button
            onClick={() => setActiveTab('gemini-md')}
            className={`px-6 py-3 font-medium transition-colors flex items-center gap-2 ${
              activeTab === 'gemini-md'
                ? 'text-blue-400 border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <FileText size={16} />
            GEMINI.md
            {hasGeminiMdChanges && <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>}
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col">
          {loading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
            </div>
          ) : (
            <>
              {/* Editor */}
              <div className="flex-1">
                {activeTab === 'settings' ? (
                  <Editor
                    height="100%"
                    defaultLanguage="json"
                    value={settingsContent}
                    onChange={(value) => {
                      setSettingsContent(value || '');
                      setValidationError('');
                    }}
                    theme="vs-dark"
                    options={{
                      minimap: { enabled: false },
                      fontSize: 14,
                      lineNumbers: 'on',
                      scrollBeyondLastLine: false,
                      automaticLayout: true,
                    }}
                  />
                ) : (
                  <Editor
                    height="100%"
                    defaultLanguage="markdown"
                    value={geminiMdContent}
                    onChange={(value) => setGeminiMdContent(value || '')}
                    theme="vs-dark"
                    options={{
                      minimap: { enabled: false },
                      fontSize: 14,
                      lineNumbers: 'on',
                      scrollBeyondLastLine: false,
                      automaticLayout: true,
                      wordWrap: 'on',
                    }}
                  />
                )}
              </div>

              {/* Error Messages */}
              {(error || validationError) && (
                <div className="p-4 border-t border-gray-700">
                  <div className="p-3 bg-red-900 border border-red-700 rounded-lg text-red-200 text-sm">
                    {error || validationError}
                  </div>
                </div>
              )}

              {/* Footer */}
              <div className="p-4 border-t border-gray-700 flex items-center justify-between">
                <div className="text-sm text-gray-400">
                  {activeTab === 'settings' ? (
                    <>
                      Edit your Gemini CLI settings. Changes will be saved to ~/.gemini/settings.json
                    </>
                  ) : (
                    <>
                      Edit your global Gemini CLI instructions. Changes will be saved to ~/.gemini/GEMINI.md
                    </>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  {activeTab === 'settings' ? (
                    <>
                      <button
                        onClick={handleResetSettings}
                        disabled={!hasSettingsChanges || saving}
                        className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-500 disabled:opacity-50 rounded-lg transition-colors"
                      >
                        <RotateCcw size={16} />
                        Reset
                      </button>
                      <button
                        onClick={handleSaveSettings}
                        disabled={!hasSettingsChanges || saving}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-lg transition-colors"
                      >
                        <Save size={16} />
                        {saving ? 'Saving...' : 'Save Settings'}
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        onClick={handleResetGeminiMd}
                        disabled={!hasGeminiMdChanges || saving}
                        className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-500 disabled:opacity-50 rounded-lg transition-colors"
                      >
                        <RotateCcw size={16} />
                        Reset
                      </button>
                      <button
                        onClick={handleSaveGeminiMd}
                        disabled={!hasGeminiMdChanges || saving}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-lg transition-colors"
                      >
                        <Save size={16} />
                        {saving ? 'Saving...' : 'Save GEMINI.md'}
                      </button>
                    </>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
