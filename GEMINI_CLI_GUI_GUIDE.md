# Building a Web GUI for Gemini CLI

This guide documents how to create a web-based graphical user interface for Google's Gemini CLI tool, based on a working implementation.

## Overview

Gemini CLI is a powerful command-line tool for interacting with Google's Gemini AI. While effective, it can benefit from a modern web interface that provides better user experience, session management, and visual feedback.

## Architecture

### Tech Stack
- **Frontend**: React with Vite
- **Backend**: Node.js with Express
- **Terminal Management**: node-pty + tmux
- **Real-time Communication**: Socket.IO
- **Package Manager**: pnpm (preferred over npm)

### Key Components
```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/
│   │   │   ├── TerminalView.jsx    # Main terminal interface
│   │   │   └── XTerminal.jsx       # xterm.js wrapper
│   │   ├── contexts/
│   │   │   └── TerminalContext.jsx # Terminal state management
│   │   └── hooks/
├── server/                 # Node.js backend
│   ├── services/
│   │   └── TerminalManager.js      # Core terminal management
│   └── index.js           # Express server + Socket.IO
```

## Critical Implementation Details

### 1. Terminal Management with tmux

The most important aspect is properly managing Gemini CLI sessions using tmux:

```javascript
// Create detached tmux session
const createSession = pty.spawn('tmux', [
  'new-session', '-d', '-s', sessionName, '-c', projectPath, 'bash'
]);

// Start Gemini CLI in the session
const startGemini = pty.spawn('tmux', [
  'send-keys', '-t', sessionName, 'gemini --prompt "..."', 'Enter'
]);

// Attach to session for output
const terminal = pty.spawn('tmux', ['attach-session', '-t', sessionName]);
```

### 2. Input Handling - The Critical Fix

**Problem**: Direct terminal input doesn't work with Gemini CLI's interactive mode.

**Solution**: Split input into two separate tmux commands:

```javascript
async sendInput(terminalId, input) {
  // Step 1: Send the text
  const sendText = pty.spawn('tmux', [
    'send-keys', '-t', sessionName, input.replace(/\r$/, '')
  ]);
  
  await waitForExit(sendText);
  await delay(100); // Critical delay
  
  // Step 2: Send Enter separately
  const sendEnter = pty.spawn('tmux', [
    'send-keys', '-t', sessionName, 'C-m'
  ]);
  
  return waitForExit(sendEnter);
}
```

### 3. Frontend Input Component

Use Cmd/Ctrl+Enter for submission (standard for terminal apps):

```jsx
const handleKeyDown = (e) => {
  if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
    e.preventDefault();
    handleSendMessage(e);
  }
};

<input
  type="text"
  onKeyDown={handleKeyDown}
  placeholder="Type your message... (⌘↵ to send)"
/>
```

### 4. Status Management

Gemini CLI takes ~5 seconds to initialize, so use time-based status updates:

```javascript
// Mark as starting initially
terminalData.status = 'starting';

// After 6 seconds, mark as running
setTimeout(() => {
  terminalData.status = 'running';
  io.emit('terminal-status', { terminalId, status: 'running' });
}, 6000);
```

## Common Pitfalls & Solutions

### ❌ Don't: Direct terminal.write()
```javascript
// This doesn't work with Gemini CLI
terminal.write(input + '\r');
```

### ✅ Do: Split tmux send-keys
```javascript
// This works reliably
await sendText(input);
await delay(100);
await sendEnter();
```

### ❌ Don't: Just Enter key
```javascript
// Too easy to accidentally submit
if (e.key === 'Enter') { submit(); }
```

### ✅ Do: Cmd/Ctrl+Enter
```javascript
// Standard terminal app pattern
if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) { submit(); }
```

### ❌ Don't: Immediate status updates
```javascript
// Gemini CLI isn't ready yet
terminal.on('data', () => setStatus('running'));
```

### ✅ Do: Time-based status
```javascript
// Give Gemini CLI time to initialize
setTimeout(() => setStatus('running'), 6000);
```

## Project Structure

### Backend Setup
```bash
mkdir gemini-gui && cd gemini-gui
mkdir server client
cd server
pnpm init
pnpm add express socket.io node-pty uuid
```

### Frontend Setup
```bash
cd ../client
pnpm create vite . --template react
pnpm add socket.io-client xterm @xterm/addon-fit lucide-react
```

## Key Dependencies

### Backend
- `node-pty`: Terminal process management
- `socket.io`: Real-time communication
- `express`: Web server
- `uuid`: Session ID generation

### Frontend
- `xterm`: Terminal display component
- `@xterm/addon-fit`: Terminal resizing
- `socket.io-client`: Real-time client
- `lucide-react`: Icons

## Testing Your Implementation

1. **Start Gemini CLI manually** to ensure it works
2. **Test tmux session creation** independently
3. **Verify input splitting** with simple echo commands
4. **Test status transitions** with timing
5. **Validate real-time updates** through Socket.IO

## Debugging Tips

1. **Log everything** during development:
   ```javascript
   console.log('[TerminalManager] Sending input:', input);
   console.log('[Frontend] Status changed:', status);
   ```

2. **Test tmux commands manually**:
   ```bash
   tmux new-session -d -s test
   tmux send-keys -t test "echo hello"
   tmux send-keys -t test C-m
   tmux capture-pane -t test -p
   ```

3. **Monitor browser console** for frontend issues
4. **Check server logs** for backend problems

## Git Worktree Integration

A key feature is automatic git worktree management:

```javascript
async setupWorktree(projectPath, sessionName) {
  // Check if git repository
  const isGitRepo = await fs.pathExists(path.join(projectPath, '.git'));

  if (!isGitRepo) return projectPath;

  // Create worktree path: ../project-sessionname
  const worktreePath = path.join(projectPath, '..', `${path.basename(projectPath)}-${sessionName}`);

  // Create worktree if it doesn't exist
  if (!await fs.pathExists(worktreePath)) {
    await spawn('git', ['worktree', 'add', worktreePath, 'HEAD'], { cwd: projectPath });
  }

  return worktreePath;
}
```

**Benefits**:
- Each session gets its own isolated working directory
- Can work on different branches/features simultaneously
- Gemini CLI operates in the correct context for each session

## Advanced Features

- **Session persistence**: Store sessions in database
- **File upload**: Handle `@file` references
- **Multi-project**: Support multiple Gemini CLI instances
- **Authentication**: Add user management
- **Themes**: Dark/light mode support
- **Git worktree management**: Automatic worktree creation per session

## Conclusion

The key to a successful Gemini CLI GUI is understanding that Gemini CLI expects interactive terminal input, not programmatic input. Using tmux as an intermediary and splitting input commands solves this fundamental challenge.

The split input approach (text first, then Enter) is the critical insight that makes everything work reliably.
