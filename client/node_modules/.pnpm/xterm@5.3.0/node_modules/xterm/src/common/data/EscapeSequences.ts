/**
 * Copyright (c) 2017 The xterm.js authors. All rights reserved.
 * @license MIT
 */

/**
 * C0 control codes
 * See = https://en.wikipedia.org/wiki/C0_and_C1_control_codes
 */
export namespace C0 {
  /** Null (Caret = ^@, C = \0) */
  export const NUL = '\x00';
  /** Start of Heading (Caret = ^A) */
  export const SOH = '\x01';
  /** Start of Text (Caret = ^B) */
  export const STX = '\x02';
  /** End of Text (Caret = ^C) */
  export const ETX = '\x03';
  /** End of Transmission (Caret = ^D) */
  export const EOT = '\x04';
  /** Enquiry (Caret = ^E) */
  export const ENQ = '\x05';
  /** Acknowledge (Caret = ^F) */
  export const ACK = '\x06';
  /** Bell (Caret = ^G, C = \a) */
  export const BEL = '\x07';
  /** Backspace (Caret = ^H, C = \b) */
  export const BS  = '\x08';
  /** Character Tabulation, Horizontal Tabulation (Caret = ^I, C = \t) */
  export const HT  = '\x09';
  /** Line Feed (Caret = ^J, C = \n) */
  export const LF  = '\x0a';
  /** Line Tabulation, Vertical Tabulation (Caret = ^K, C = \v) */
  export const VT  = '\x0b';
  /** Form Feed (Caret = ^L, C = \f) */
  export const FF  = '\x0c';
  /** Carriage Return (Caret = ^M, C = \r) */
  export const CR  = '\x0d';
  /** Shift Out (Caret = ^N) */
  export const SO  = '\x0e';
  /** Shift In (Caret = ^O) */
  export const SI  = '\x0f';
  /** Data Link Escape (Caret = ^P) */
  export const DLE = '\x10';
  /** Device Control One (XON) (Caret = ^Q) */
  export const DC1 = '\x11';
  /** Device Control Two (Caret = ^R) */
  export const DC2 = '\x12';
  /** Device Control Three (XOFF) (Caret = ^S) */
  export const DC3 = '\x13';
  /** Device Control Four (Caret = ^T) */
  export const DC4 = '\x14';
  /** Negative Acknowledge (Caret = ^U) */
  export const NAK = '\x15';
  /** Synchronous Idle (Caret = ^V) */
  export const SYN = '\x16';
  /** End of Transmission Block (Caret = ^W) */
  export const ETB = '\x17';
  /** Cancel (Caret = ^X) */
  export const CAN = '\x18';
  /** End of Medium (Caret = ^Y) */
  export const EM  = '\x19';
  /** Substitute (Caret = ^Z) */
  export const SUB = '\x1a';
  /** Escape (Caret = ^[, C = \e) */
  export const ESC = '\x1b';
  /** File Separator (Caret = ^\) */
  export const FS  = '\x1c';
  /** Group Separator (Caret = ^]) */
  export const GS  = '\x1d';
  /** Record Separator (Caret = ^^) */
  export const RS  = '\x1e';
  /** Unit Separator (Caret = ^_) */
  export const US  = '\x1f';
  /** Space */
  export const SP  = '\x20';
  /** Delete (Caret = ^?) */
  export const DEL = '\x7f';
}

/**
 * C1 control codes
 * See = https://en.wikipedia.org/wiki/C0_and_C1_control_codes
 */
export namespace C1 {
  /** padding character */
  export const PAD = '\x80';
  /** High Octet Preset */
  export const HOP = '\x81';
  /** Break Permitted Here */
  export const BPH = '\x82';
  /** No Break Here */
  export const NBH = '\x83';
  /** Index */
  export const IND = '\x84';
  /** Next Line */
  export const NEL = '\x85';
  /** Start of Selected Area */
  export const SSA = '\x86';
  /** End of Selected Area */
  export const ESA = '\x87';
  /** Horizontal Tabulation Set */
  export const HTS = '\x88';
  /** Horizontal Tabulation With Justification */
  export const HTJ = '\x89';
  /** Vertical Tabulation Set */
  export const VTS = '\x8a';
  /** Partial Line Down */
  export const PLD = '\x8b';
  /** Partial Line Up */
  export const PLU = '\x8c';
  /** Reverse Index */
  export const RI = '\x8d';
  /** Single-Shift 2 */
  export const SS2 = '\x8e';
  /** Single-Shift 3 */
  export const SS3 = '\x8f';
  /** Device Control String */
  export const DCS = '\x90';
  /** Private Use 1 */
  export const PU1 = '\x91';
  /** Private Use 2 */
  export const PU2 = '\x92';
  /** Set Transmit State */
  export const STS = '\x93';
  /** Destructive backspace, intended to eliminate ambiguity about meaning of BS. */
  export const CCH = '\x94';
  /** Message Waiting */
  export const MW = '\x95';
  /** Start of Protected Area */
  export const SPA = '\x96';
  /** End of Protected Area */
  export const EPA = '\x97';
  /** Start of String */
  export const SOS = '\x98';
  /** Single Graphic Character Introducer */
  export const SGCI = '\x99';
  /** Single Character Introducer */
  export const SCI = '\x9a';
  /** Control Sequence Introducer */
  export const CSI = '\x9b';
  /** String Terminator */
  export const ST = '\x9c';
  /** Operating System Command */
  export const OSC = '\x9d';
  /** Privacy Message */
  export const PM = '\x9e';
  /** Application Program Command */
  export const APC = '\x9f';
}
export namespace C1_ESCAPED {
  export const ST = `${C0.ESC}\\`;
}
