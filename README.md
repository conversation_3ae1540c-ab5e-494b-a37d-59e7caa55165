# Gemini CLI GUI

A modern web-based GUI for the [Gemini CLI](https://github.com/google-gemini/gemini-cli) that provides an intuitive interface for managing projects, sessions, and terminal interactions.

## Features

- 🚀 **Project Management**: Create and organize multiple projects with their local paths
- 📁 **Session Management**: Start multiple sessions per project with custom prompts
- 💻 **Terminal Interface**: Real-time terminal interaction with Gemini CLI
- ⚙️ **Settings Editor**: Visual editor for `~/.gemini/settings.json` and `GEMINI.md` files
- 🔄 **Session Persistence**: Uses tmux for persistent terminal sessions
- 🎨 **Modern UI**: Clean, responsive interface built with <PERSON>act and Tailwind CSS

## Prerequisites

- **Node.js** (v18 or higher)
- **pnpm** (preferred package manager)
- **Gemini CLI** installed and configured (`gemini` command available in PATH)
- **tmux** (for session management)

## Installation

1. Clone this repository:
   ```bash
   git clone <repository-url>
   cd gemini-cli-gui
   ```

2. Install dependencies:
   ```bash
   pnpm install
   cd server && pnpm install
   cd ../client && pnpm install
   ```

3. Approve build scripts when prompted:
   ```bash
   # In server directory
   pnpm approve-builds
   # In client directory  
   pnpm approve-builds
   ```

## Usage

### Starting the Application

1. Start both backend and frontend servers:
   ```bash
   pnpm run dev
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

### Using the GUI

#### Creating a Project

1. Click the "New Project" button in the sidebar
2. Enter a project name
3. Select or enter the local folder path for your project
4. Click "Add Project"

#### Starting a Session

1. Select a project from the sidebar
2. Click the "New Session" button (play icon) next to the project name
3. Enter a session name (spaces will be converted to dashes)
4. Optionally add an initial prompt for Gemini CLI
5. Click "Start Session"

#### Terminal Interaction

- Once a session is started, you'll see the terminal interface
- Type commands or messages in the input field at the bottom
- Press Enter to send input to Gemini CLI
- Use standard Gemini CLI commands like `/help`, `/clear`, etc.

#### Settings Management

1. Click the settings icon in the sidebar header
2. Edit your `settings.json` configuration with syntax highlighting
3. Edit your global `GEMINI.md` instructions
4. Save changes to apply them to Gemini CLI

## Architecture

### Backend (Node.js + Express)
- **Terminal Management**: Uses `node-pty` for terminal emulation
- **Session Persistence**: Integrates with tmux for persistent sessions
- **API Endpoints**: RESTful API for project/session management
- **WebSocket Communication**: Real-time terminal output streaming

### Frontend (React + Vite)
- **Modern React**: Hooks-based components with context for state management
- **Real-time Updates**: WebSocket integration for live terminal output
- **Monaco Editor**: Professional code editor for settings files
- **Responsive Design**: Tailwind CSS for modern, mobile-friendly UI

## API Endpoints

- `GET /api/health` - Health check
- `GET /api/projects` - List all projects
- `POST /api/projects` - Create new project
- `GET /api/projects/:id/sessions` - List project sessions
- `POST /api/projects/:id/sessions` - Create new session
- `POST /api/terminal/start` - Start terminal session
- `POST /api/terminal/:id/input` - Send input to terminal
- `GET /api/settings` - Get Gemini CLI settings
- `PUT /api/settings` - Update settings

## Development

### Project Structure
```
gemini-cli-gui/
├── server/                 # Backend Node.js application
│   ├── services/          # Business logic services
│   └── index.js          # Main server file
├── client/                # Frontend React application
│   ├── src/
│   │   ├── components/   # React components
│   │   ├── contexts/     # React context providers
│   │   └── App.jsx      # Main app component
└── package.json          # Root package.json with scripts
```

### Available Scripts

- `pnpm run dev` - Start both backend and frontend in development mode
- `pnpm run build` - Build the frontend for production
- `pnpm run start` - Start the backend in production mode

## Troubleshooting

### Gemini CLI Not Found
Ensure Gemini CLI is installed and available in your PATH:
```bash
which gemini
gemini --version
```

### tmux Issues
Make sure tmux is installed:
```bash
# macOS
brew install tmux

# Ubuntu/Debian
sudo apt-get install tmux
```

### Port Conflicts
If ports 3000 or 3001 are in use, you can modify them in:
- Frontend: `client/vite.config.js`
- Backend: `server/index.js`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details
