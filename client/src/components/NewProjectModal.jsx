import React, { useState } from 'react';
import { X, Folder } from 'lucide-react';
import { useProjects } from '../contexts/ProjectContext';

const NewProjectModal = ({ isOpen, onClose }) => {
  const { createProject } = useProjects();
  const [formData, setFormData] = useState({
    name: '',
    path: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.path.trim()) {
      setError('Both name and path are required');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await createProject(formData.name.trim(), formData.path.trim());
      onClose();
      setFormData({ name: '', path: '' });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePathSelect = async () => {
    try {
      // Use the File System Access API if available (Chrome/Edge)
      if ('showDirectoryPicker' in window) {
        const dirHandle = await window.showDirectoryPicker();

        // Try to get the full path if possible
        // Note: Full path access is limited in browsers for security
        // We'll need to ask user to enter the full path manually
        alert(`Selected folder: ${dirHandle.name}\n\nFor security reasons, browsers don't provide the full path. Please enter the complete path manually in the field below.`);

        // Just clear the field so user knows to enter the full path
        setFormData(prev => ({ ...prev, path: '' }));
      } else {
        // Fallback: show a message about manual entry
        alert('Please enter the path manually. Directory picker is not supported in this browser.');
      }
    } catch (err) {
      // User cancelled or error occurred
      console.log('Directory selection cancelled or failed:', err);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">New Project</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Project Name */}
          <div>
            <label htmlFor="projectName" className="block text-sm font-medium mb-2">
              Project Name
            </label>
            <input
              id="projectName"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter project name"
              disabled={loading}
            />
          </div>

          {/* Project Path */}
          <div>
            <label htmlFor="projectPath" className="block text-sm font-medium mb-2">
              Repository Path
            </label>
            <div className="flex gap-2">
              <input
                id="projectPath"
                type="text"
                value={formData.path}
                onChange={(e) => setFormData(prev => ({ ...prev, path: e.target.value }))}
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="/Users/<USER>/Documents/your-project"
                disabled={loading}
              />
              <button
                type="button"
                onClick={handlePathSelect}
                className="px-3 py-2 bg-gray-600 hover:bg-gray-500 rounded-lg transition-colors flex items-center gap-2"
                disabled={loading}
                title="Select folder"
              >
                <Folder size={16} />
              </button>
            </div>
            <p className="text-xs text-gray-400 mt-1">
              Full absolute path to the local folder containing your project
            </p>
            <p className="text-xs text-yellow-400 mt-1">
              💡 Tip: You can find the full path by opening Terminal, navigating to your folder, and running <code className="bg-gray-600 px-1 rounded">pwd</code>
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-900 border border-red-700 rounded-lg text-red-200 text-sm">
              {error}
            </div>
          )}

          {/* Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 rounded-lg transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
              disabled={loading || !formData.name.trim() || !formData.path.trim()}
            >
              {loading ? 'Creating...' : 'Add Project'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewProjectModal;
